// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

// User model - extends Appwrite user data
model User {
  id        String   @id @default(cuid())
  appwriteId String  @unique // Links to Appwrite user ID
  email     String   @unique
  username  String   @unique
  displayName String?
  bio       String?
  avatar    String?
  role      UserRole @default(FAN)
  isVerified <PERSON><PERSON><PERSON> @default(false)
  
  // Timestamps
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
  
  // Relations
  creator   Creator?
  posts     Post[]
  reactions Reaction[]
  memories  Memory[]
  followers Follow[] @relation("UserFollowers")
  following Follow[] @relation("UserFollowing")
  
  @@map("users")
}

// Creator profile - for content creators
model Creator {
  id          String  @id @default(cuid())
  userId      String  @unique
  user        User    @relation(fields: [userId], references: [id], onDelete: Cascade)
  
  // Creator-specific fields
  stageName   String?
  genre       String[]
  location    String?
  website     String?
  socialLinks Json?
  
  // Analytics
  totalFollowers Int @default(0)
  totalViews     Int @default(0)
  totalLikes     Int @default(0)
  
  // Timestamps
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
  
  // Relations
  personas Persona[]
  posts    Post[]
  
  @@map("creators")
}

// Persona model - multiple identities per creator
model Persona {
  id          String  @id @default(cuid())
  creatorId   String
  creator     Creator @relation(fields: [creatorId], references: [id], onDelete: Cascade)
  
  name        String
  description String?
  avatar      String?
  coverImage  String?
  mood        String[] // Associated moods
  isActive    Boolean @default(true)
  
  // Timestamps
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
  
  // Relations
  posts Post[]
  
  @@map("personas")
}

// Post model - main content posts
model Post {
  id          String      @id @default(cuid())
  userId      String
  user        User        @relation(fields: [userId], references: [id], onDelete: Cascade)
  creatorId   String?
  creator     Creator?    @relation(fields: [creatorId], references: [id], onDelete: SetNull)
  personaId   String?
  persona     Persona?    @relation(fields: [personaId], references: [id], onDelete: SetNull)
  
  // Content
  title       String?
  content     String
  contentType ContentType
  mediaUrls   String[]
  thumbnailUrl String?
  
  // Emotional AI
  moods       String[]
  aiAnalysis  Json? // AI-generated mood and content analysis
  
  // Engagement
  viewCount   Int @default(0)
  likeCount   Int @default(0)
  shareCount  Int @default(0)
  
  // Visibility
  isPublic    Boolean @default(true)
  isExperimental Boolean @default(false) // For experimental content lab
  
  // Timestamps
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
  publishedAt DateTime?
  
  // Relations
  reactions Reaction[]
  memories  Memory[]
  
  @@map("posts")
}

// Reaction model - likes, loves, etc.
model Reaction {
  id     String      @id @default(cuid())
  userId String
  user   User        @relation(fields: [userId], references: [id], onDelete: Cascade)
  postId String
  post   Post        @relation(fields: [postId], references: [id], onDelete: Cascade)
  
  type   ReactionType
  mood   String? // User's mood when reacting
  
  createdAt DateTime @default(now())
  
  @@unique([userId, postId])
  @@map("reactions")
}

// Memory model - fan-saved moments
model Memory {
  id          String @id @default(cuid())
  userId      String
  user        User   @relation(fields: [userId], references: [id], onDelete: Cascade)
  postId      String
  post        Post   @relation(fields: [postId], references: [id], onDelete: Cascade)
  
  title       String?
  description String?
  timestamp   Float? // Timestamp in media for video/audio clips
  thumbnailUrl String?
  
  createdAt DateTime @default(now())
  
  @@map("memories")
}

// Follow model - user relationships
model Follow {
  id          String @id @default(cuid())
  followerId  String
  follower    User   @relation("UserFollowing", fields: [followerId], references: [id], onDelete: Cascade)
  followingId String
  following   User   @relation("UserFollowers", fields: [followingId], references: [id], onDelete: Cascade)
  
  createdAt DateTime @default(now())
  
  @@unique([followerId, followingId])
  @@map("follows")
}

// Enums
enum UserRole {
  FAN
  CREATOR
  ADMIN
}

enum ContentType {
  MUSIC
  VIDEO
  IMAGE
  TEXT
  STORY
  LIVE
  MEMORY
}

enum ReactionType {
  LIKE
  LOVE
  FIRE
  MIND_BLOWN
  VIBE
  MOOD_MATCH
}
