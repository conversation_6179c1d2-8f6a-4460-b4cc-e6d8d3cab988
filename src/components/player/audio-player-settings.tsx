"use client"

import React, { useState, useEffect } from 'react'
import { cn } from '@/lib/utils'
import { AudioPlayer, AudioQuality } from '@/lib/player/types'
import { getQualityLabel } from '@/lib/player/utils'
import { But<PERSON> } from '@/components/ui/button'
import { Slider } from '@/components/ui/slider'
import { Switch } from '@/components/ui/switch'
import { 
  X, 
  Volume2, 
  Gauge, 
  Equalizer,
  ChevronRight,
  ChevronLeft,
  Check,
  RotateCcw
} from 'lucide-react'

interface AudioPlayerSettingsProps {
  player: AudioPlayer
  onClose: () => void
  className?: string
}

type SettingsPanel = 'main' | 'quality' | 'equalizer' | 'effects'

const EQUALIZER_BANDS = [
  { frequency: '60Hz', label: '60' },
  { frequency: '170Hz', label: '170' },
  { frequency: '310Hz', label: '310' },
  { frequency: '600Hz', label: '600' },
  { frequency: '1kHz', label: '1K' },
  { frequency: '3kHz', label: '3K' },
  { frequency: '6kHz', label: '6K' },
  { frequency: '12kHz', label: '12K' },
  { frequency: '14kHz', label: '14K' },
  { frequency: '16kHz', label: '16K' }
]

const EQUALIZER_PRESETS = [
  { name: 'Flat', key: 'flat' },
  { name: 'Rock', key: 'rock' },
  { name: 'Pop', key: 'pop' },
  { name: 'Jazz', key: 'jazz' },
  { name: 'Classical', key: 'classical' },
  { name: 'Electronic', key: 'electronic' },
  { name: 'Hip Hop', key: 'hip_hop' },
  { name: 'Vocal', key: 'vocal' }
]

export function AudioPlayerSettings({
  player,
  onClose,
  className
}: AudioPlayerSettingsProps) {
  const [playerState, setPlayerState] = useState(player.getState())
  const [currentPanel, setCurrentPanel] = useState<SettingsPanel>('main')
  const [equalizerBands, setEqualizerBands] = useState<number[]>([0, 0, 0, 0, 0, 0, 0, 0, 0, 0])
  const [selectedPreset, setSelectedPreset] = useState<string>('flat')

  useEffect(() => {
    const handleStateChange = (state: any) => {
      setPlayerState(state)
    }

    player.on('onStateChange', handleStateChange)
    
    // Get current equalizer settings
    const currentEQ = player.getEqualizer()
    setEqualizerBands(currentEQ)

    return () => player.off('onStateChange', handleStateChange)
  }, [player])

  const handleQualityChange = (quality: AudioQuality) => {
    player.setQuality(quality)
    setCurrentPanel('main')
  }

  const handlePlaybackRateChange = (rate: number) => {
    player.setPlaybackRate(rate)
  }

  const handleEqualizerChange = (bandIndex: number, value: number) => {
    const newBands = [...equalizerBands]
    newBands[bandIndex] = value
    setEqualizerBands(newBands)
    player.setEqualizer(newBands)
    setSelectedPreset('custom')
  }

  const handlePresetChange = (presetKey: string) => {
    setSelectedPreset(presetKey)
    player.setEqualizerPreset(presetKey)
    
    // Update local state to reflect preset
    const newBands = player.getEqualizer()
    setEqualizerBands(newBands)
    setCurrentPanel('main')
  }

  const resetEqualizer = () => {
    const flatBands = [0, 0, 0, 0, 0, 0, 0, 0, 0, 0]
    setEqualizerBands(flatBands)
    player.setEqualizer(flatBands)
    setSelectedPreset('flat')
  }

  const availableQualities: AudioQuality[] = [
    AudioQuality.LOSSLESS,
    AudioQuality.HIGH,
    AudioQuality.MEDIUM,
    AudioQuality.LOW
  ]

  const playbackRates = [0.5, 0.75, 1, 1.25, 1.5, 2]

  const renderMainPanel = () => (
    <div className="space-y-4">
      <h3 className="text-lg font-semibold text-white mb-4">Audio Settings</h3>
      
      {/* Quality Setting */}
      <div 
        className="flex items-center justify-between p-3 rounded-lg bg-white/5 hover:bg-white/10 cursor-pointer transition-colors"
        onClick={() => setCurrentPanel('quality')}
      >
        <div className="flex items-center space-x-3">
          <Volume2 className="w-5 h-5 text-white/70" />
          <div>
            <div className="text-white font-medium">Audio Quality</div>
            <div className="text-white/70 text-sm">
              {getQualityLabel(playerState.quality)}
            </div>
          </div>
        </div>
        <ChevronRight className="w-5 h-5 text-white/70" />
      </div>

      {/* Playback Speed */}
      <div 
        className="flex items-center justify-between p-3 rounded-lg bg-white/5 hover:bg-white/10 cursor-pointer transition-colors"
        onClick={() => setCurrentPanel('effects')}
      >
        <div className="flex items-center space-x-3">
          <Gauge className="w-5 h-5 text-white/70" />
          <div>
            <div className="text-white font-medium">Playback Speed</div>
            <div className="text-white/70 text-sm">
              {playerState.playbackRate}x
            </div>
          </div>
        </div>
        <ChevronRight className="w-5 h-5 text-white/70" />
      </div>

      {/* Equalizer */}
      <div 
        className="flex items-center justify-between p-3 rounded-lg bg-white/5 hover:bg-white/10 cursor-pointer transition-colors"
        onClick={() => setCurrentPanel('equalizer')}
      >
        <div className="flex items-center space-x-3">
          <Equalizer className="w-5 h-5 text-white/70" />
          <div>
            <div className="text-white font-medium">Equalizer</div>
            <div className="text-white/70 text-sm">
              {selectedPreset === 'custom' ? 'Custom' : EQUALIZER_PRESETS.find(p => p.key === selectedPreset)?.name || 'Flat'}
            </div>
          </div>
        </div>
        <ChevronRight className="w-5 h-5 text-white/70" />
      </div>

      {/* Volume Control */}
      <div className="p-3 rounded-lg bg-white/5">
        <div className="flex items-center space-x-3 mb-3">
          <Volume2 className="w-5 h-5 text-white/70" />
          <div className="text-white font-medium">Volume</div>
        </div>
        <div className="space-y-3">
          <Slider
            value={[playerState.muted ? 0 : playerState.volume * 100]}
            onValueChange={(value) => {
              const volume = value[0] / 100
              player.setVolume(volume)
              if (volume > 0 && playerState.muted) {
                player.setMuted(false)
              }
            }}
            max={100}
            step={1}
            className="w-full"
            trackClassName="bg-white/20"
            rangeClassName="bg-purple-500"
            thumbClassName="bg-purple-500 border-2 border-white"
          />
          <div className="flex items-center justify-between">
            <span className="text-white/70 text-sm">Muted</span>
            <Switch
              checked={!playerState.muted}
              onCheckedChange={(checked) => player.setMuted(!checked)}
            />
          </div>
        </div>
      </div>
    </div>
  )

  const renderQualityPanel = () => (
    <div className="space-y-4">
      <div className="flex items-center space-x-3 mb-4">
        <Button
          variant="ghost"
          size="sm"
          onClick={() => setCurrentPanel('main')}
          className="text-white hover:bg-white/20 p-2"
        >
          <ChevronLeft className="w-4 h-4" />
        </Button>
        <h3 className="text-lg font-semibold text-white">Audio Quality</h3>
      </div>

      <div className="space-y-2">
        {availableQualities.map((quality) => (
          <div
            key={quality}
            className="flex items-center justify-between p-3 rounded-lg bg-white/5 hover:bg-white/10 cursor-pointer transition-colors"
            onClick={() => handleQualityChange(quality)}
          >
            <span className="text-white">{getQualityLabel(quality)}</span>
            {playerState.quality === quality && (
              <Check className="w-5 h-5 text-purple-500" />
            )}
          </div>
        ))}
      </div>
    </div>
  )

  const renderEqualizerPanel = () => (
    <div className="space-y-4">
      <div className="flex items-center justify-between mb-4">
        <div className="flex items-center space-x-3">
          <Button
            variant="ghost"
            size="sm"
            onClick={() => setCurrentPanel('main')}
            className="text-white hover:bg-white/20 p-2"
          >
            <ChevronLeft className="w-4 h-4" />
          </Button>
          <h3 className="text-lg font-semibold text-white">Equalizer</h3>
        </div>
        
        <Button
          variant="ghost"
          size="sm"
          onClick={resetEqualizer}
          className="text-white hover:bg-white/20 p-2"
        >
          <RotateCcw className="w-4 h-4" />
        </Button>
      </div>

      {/* Presets */}
      <div className="space-y-2 mb-6">
        <h4 className="text-white font-medium text-sm">Presets</h4>
        <div className="grid grid-cols-2 gap-2">
          {EQUALIZER_PRESETS.map((preset) => (
            <Button
              key={preset.key}
              variant={selectedPreset === preset.key ? "default" : "outline"}
              size="sm"
              onClick={() => handlePresetChange(preset.key)}
              className={cn(
                "text-xs",
                selectedPreset === preset.key 
                  ? "bg-purple-500 text-white" 
                  : "text-white border-white/20 hover:bg-white/10"
              )}
            >
              {preset.name}
            </Button>
          ))}
        </div>
      </div>

      {/* EQ Bands */}
      <div className="space-y-4">
        <h4 className="text-white font-medium text-sm">Manual Adjustment</h4>
        <div className="grid grid-cols-5 gap-3">
          {EQUALIZER_BANDS.map((band, index) => (
            <div key={index} className="text-center">
              <div className="h-32 flex items-end justify-center mb-2">
                <Slider
                  value={[equalizerBands[index]]}
                  onValueChange={(value) => handleEqualizerChange(index, value[0])}
                  min={-20}
                  max={20}
                  step={0.5}
                  orientation="vertical"
                  className="h-full"
                  trackClassName="bg-white/20"
                  rangeClassName="bg-purple-500"
                  thumbClassName="bg-purple-500 border-2 border-white"
                />
              </div>
              <div className="text-white/70 text-xs">{band.label}</div>
              <div className="text-white/50 text-xs">
                {equalizerBands[index] > 0 ? '+' : ''}{equalizerBands[index].toFixed(1)}dB
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  )

  const renderEffectsPanel = () => (
    <div className="space-y-4">
      <div className="flex items-center space-x-3 mb-4">
        <Button
          variant="ghost"
          size="sm"
          onClick={() => setCurrentPanel('main')}
          className="text-white hover:bg-white/20 p-2"
        >
          <ChevronLeft className="w-4 h-4" />
        </Button>
        <h3 className="text-lg font-semibold text-white">Effects & Speed</h3>
      </div>

      {/* Playback Speed */}
      <div className="space-y-3">
        <h4 className="text-white font-medium">Playback Speed</h4>
        <div className="space-y-2">
          {playbackRates.map((rate) => (
            <div
              key={rate}
              className="flex items-center justify-between p-3 rounded-lg bg-white/5 hover:bg-white/10 cursor-pointer transition-colors"
              onClick={() => handlePlaybackRateChange(rate)}
            >
              <span className="text-white">{rate}x</span>
              {playerState.playbackRate === rate && (
                <Check className="w-5 h-5 text-purple-500" />
              )}
            </div>
          ))}
        </div>
      </div>

      {/* Audio Effects */}
      <div className="space-y-3">
        <h4 className="text-white font-medium">Audio Effects</h4>
        
        <div className="p-3 rounded-lg bg-white/5">
          <div className="flex items-center justify-between mb-2">
            <span className="text-white text-sm">Bass Boost</span>
          </div>
          <Slider
            value={[0]}
            onValueChange={(value) => player.setBassBoost(value[0])}
            min={-10}
            max={10}
            step={0.5}
            className="w-full"
            trackClassName="bg-white/20"
            rangeClassName="bg-purple-500"
            thumbClassName="bg-purple-500 border-2 border-white"
          />
        </div>

        <div className="p-3 rounded-lg bg-white/5">
          <div className="flex items-center justify-between mb-2">
            <span className="text-white text-sm">Treble Boost</span>
          </div>
          <Slider
            value={[0]}
            onValueChange={(value) => player.setTrebleBoost(value[0])}
            min={-10}
            max={10}
            step={0.5}
            className="w-full"
            trackClassName="bg-white/20"
            rangeClassName="bg-purple-500"
            thumbClassName="bg-purple-500 border-2 border-white"
          />
        </div>
      </div>
    </div>
  )

  const renderCurrentPanel = () => {
    switch (currentPanel) {
      case 'quality':
        return renderQualityPanel()
      case 'equalizer':
        return renderEqualizerPanel()
      case 'effects':
        return renderEffectsPanel()
      default:
        return renderMainPanel()
    }
  }

  return (
    <div className={cn(
      'absolute inset-0 bg-black/80 backdrop-blur-sm flex items-center justify-center p-4 z-50',
      className
    )}>
      <div className="bg-gray-900/95 backdrop-blur-md rounded-lg border border-white/10 w-full max-w-md max-h-[80vh] overflow-hidden">
        {/* Header */}
        <div className="flex items-center justify-between p-4 border-b border-white/10">
          <div className="w-6" /> {/* Spacer */}
          <h2 className="text-white font-semibold">Audio Settings</h2>
          <Button
            variant="ghost"
            size="sm"
            onClick={onClose}
            className="text-white hover:bg-white/20 p-2"
          >
            <X className="w-4 h-4" />
          </Button>
        </div>

        {/* Content */}
        <div className="p-4 overflow-y-auto max-h-[60vh]">
          {renderCurrentPanel()}
        </div>
      </div>
    </div>
  )
}
