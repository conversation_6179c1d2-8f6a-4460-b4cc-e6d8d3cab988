// Core types for HVPPY Central

export interface User {
  id: string
  appwriteId: string
  email: string
  username: string
  displayName?: string
  bio?: string
  avatar?: string
  role: UserRole
  isVerified: boolean
  createdAt: Date
  updatedAt: Date
}

export interface Creator {
  id: string
  userId: string
  user: User
  stageName?: string
  genre: string[]
  location?: string
  website?: string
  socialLinks?: Record<string, string>
  totalFollowers: number
  totalViews: number
  totalLikes: number
  createdAt: Date
  updatedAt: Date
  personas: Persona[]
}

export interface Persona {
  id: string
  creatorId: string
  creator: Creator
  name: string
  description?: string
  avatar?: string
  coverImage?: string
  mood: string[]
  isActive: boolean
  createdAt: Date
  updatedAt: Date
}

export interface Post {
  id: string
  userId: string
  user: User
  creatorId?: string
  creator?: Creator
  personaId?: string
  persona?: Persona
  title?: string
  content: string
  contentType: ContentType
  mediaUrls: string[]
  thumbnailUrl?: string
  moods: string[]
  aiAnalysis?: Record<string, any>
  viewCount: number
  likeCount: number
  shareCount: number
  isPublic: boolean
  isExperimental: boolean
  createdAt: Date
  updatedAt: Date
  publishedAt?: Date
  reactions: Reaction[]
  memories: Memory[]
}

export interface Reaction {
  id: string
  userId: string
  user: User
  postId: string
  post: Post
  type: ReactionType
  mood?: string
  createdAt: Date
}

export interface Memory {
  id: string
  userId: string
  user: User
  postId: string
  post: Post
  title?: string
  description?: string
  timestamp?: number
  thumbnailUrl?: string
  createdAt: Date
}

export interface Follow {
  id: string
  followerId: string
  follower: User
  followingId: string
  following: User
  createdAt: Date
}

// Enums
export enum UserRole {
  FAN = 'FAN',
  CREATOR = 'CREATOR',
  ADMIN = 'ADMIN'
}

export enum ContentType {
  MUSIC = 'MUSIC',
  VIDEO = 'VIDEO',
  IMAGE = 'IMAGE',
  TEXT = 'TEXT',
  STORY = 'STORY',
  LIVE = 'LIVE',
  MEMORY = 'MEMORY'
}

export enum ReactionType {
  LIKE = 'LIKE',
  LOVE = 'LOVE',
  FIRE = 'FIRE',
  MIND_BLOWN = 'MIND_BLOWN',
  VIBE = 'VIBE',
  MOOD_MATCH = 'MOOD_MATCH'
}

// Mood types for emotional AI
export type MoodType = 
  | 'happy'
  | 'chill'
  | 'heartbroken'
  | 'inspired'
  | 'energetic'
  | 'peaceful'
  | 'nostalgic'
  | 'excited'

// API Response types
export interface ApiResponse<T = any> {
  success: boolean
  data?: T
  error?: string
  message?: string
}

// Form types
export interface CreatePostForm {
  title?: string
  content: string
  contentType: ContentType
  moods: string[]
  isPublic: boolean
  isExperimental: boolean
  personaId?: string
}

export interface CreatePersonaForm {
  name: string
  description?: string
  mood: string[]
}

export interface UpdateProfileForm {
  displayName?: string
  bio?: string
  stageName?: string
  genre?: string[]
  location?: string
  website?: string
  socialLinks?: Record<string, string>
}

// Component props
export interface MoodSelectorProps {
  selectedMoods: string[]
  onMoodChange: (moods: string[]) => void
  maxSelections?: number
}

export interface ContentCardProps {
  post: Post
  showActions?: boolean
  compact?: boolean
}

export interface UserAvatarProps {
  user: User
  size?: 'sm' | 'md' | 'lg'
  showStatus?: boolean
}

// Utility types
export type WithOptional<T, K extends keyof T> = Omit<T, K> & Partial<Pick<T, K>>
export type WithRequired<T, K extends keyof T> = T & Required<Pick<T, K>>
